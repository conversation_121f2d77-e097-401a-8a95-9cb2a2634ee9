# Task3.1.1 Javascript Unit Tests Creation Template


Using LLM, cover the provided JavaScript function with the comprehensive unit tests. Cover all possible test cases, including edge scenarios, to ensure the function works as expected. Use the Mocha JavaScript testing framework. Organize your tests into appropriate test suites for clarity.

The function to test is in the template.

It is recommended that you complete this task with the help of [EPAM Dial](https://chat.lab.epam.com/).