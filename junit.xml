<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="0.0030" tests="40" failures="0">
  <testsuite name="Root Suite" timestamp="2025-06-17T13:49:20" tests="0" time="0.0000" failures="0">
  </testsuite>
  <testsuite name="calculateDiscountedPrice" timestamp="2025-06-17T13:49:20" tests="0" file="/home/<USER>/learn-to-code/task3.1.1-javascript-unit-tests-creation-copilot/test/index.test.js" time="0.0000" failures="0">
  </testsuite>
  <testsuite name="Valid Input Tests" timestamp="2025-06-17T13:49:20" tests="11" file="/home/<USER>/learn-to-code/task3.1.1-javascript-unit-tests-creation-copilot/test/index.test.js" time="0.0000" failures="0">
    <testcase name="calculateDiscountedPrice Valid Input Tests should calculate correct discounted price for normal values" time="0.0000" classname="should calculate correct discounted price for normal values">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle zero discount percentage" time="0.0000" classname="should handle zero discount percentage">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle 100% discount" time="0.0000" classname="should handle 100% discount">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle decimal original prices" time="0.0000" classname="should handle decimal original prices">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle decimal discount percentages" time="0.0000" classname="should handle decimal discount percentages">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle very small original prices" time="0.0000" classname="should handle very small original prices">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle very large original prices" time="0.0000" classname="should handle very large original prices">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle fractional discount percentages" time="0.0000" classname="should handle fractional discount percentages">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should return zero when original price is zero" time="0.0000" classname="should return zero when original price is zero">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle very small discount percentages" time="0.0000" classname="should handle very small discount percentages">
    </testcase>
    <testcase name="calculateDiscountedPrice Valid Input Tests should handle discount percentage close to 100" time="0.0000" classname="should handle discount percentage close to 100">
    </testcase>
  </testsuite>
  <testsuite name="Input Type Validation Tests" timestamp="2025-06-17T13:49:20" tests="13" file="/home/<USER>/learn-to-code/task3.1.1-javascript-unit-tests-creation-copilot/test/index.test.js" time="0.0020" failures="0">
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when originalPrice is not a number" time="0.0010" classname="should throw error when originalPrice is not a number">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when discountPercentage is not a number" time="0.0000" classname="should throw error when discountPercentage is not a number">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when originalPrice is null" time="0.0000" classname="should throw error when originalPrice is null">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when discountPercentage is null" time="0.0010" classname="should throw error when discountPercentage is null">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when originalPrice is undefined" time="0.0000" classname="should throw error when originalPrice is undefined">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when discountPercentage is undefined" time="0.0000" classname="should throw error when discountPercentage is undefined">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when originalPrice is boolean" time="0.0000" classname="should throw error when originalPrice is boolean">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when discountPercentage is boolean" time="0.0000" classname="should throw error when discountPercentage is boolean">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when originalPrice is an array" time="0.0000" classname="should throw error when originalPrice is an array">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when discountPercentage is an array" time="0.0000" classname="should throw error when discountPercentage is an array">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when originalPrice is an object" time="0.0000" classname="should throw error when originalPrice is an object">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when discountPercentage is an object" time="0.0000" classname="should throw error when discountPercentage is an object">
    </testcase>
    <testcase name="calculateDiscountedPrice Input Type Validation Tests should throw error when both parameters are invalid types" time="0.0000" classname="should throw error when both parameters are invalid types">
    </testcase>
  </testsuite>
  <testsuite name="Discount Percentage Range Validation Tests" timestamp="2025-06-17T13:49:20" tests="6" file="/home/<USER>/learn-to-code/task3.1.1-javascript-unit-tests-creation-copilot/test/index.test.js" time="0.0000" failures="0">
    <testcase name="calculateDiscountedPrice Discount Percentage Range Validation Tests should throw error when discount percentage is negative" time="0.0000" classname="should throw error when discount percentage is negative">
    </testcase>
    <testcase name="calculateDiscountedPrice Discount Percentage Range Validation Tests should throw error when discount percentage is greater than 100" time="0.0000" classname="should throw error when discount percentage is greater than 100">
    </testcase>
    <testcase name="calculateDiscountedPrice Discount Percentage Range Validation Tests should throw error when discount percentage is very negative" time="0.0000" classname="should throw error when discount percentage is very negative">
    </testcase>
    <testcase name="calculateDiscountedPrice Discount Percentage Range Validation Tests should throw error when discount percentage is much greater than 100" time="0.0000" classname="should throw error when discount percentage is much greater than 100">
    </testcase>
    <testcase name="calculateDiscountedPrice Discount Percentage Range Validation Tests should throw error when discount percentage is negative infinity" time="0.0000" classname="should throw error when discount percentage is negative infinity">
    </testcase>
    <testcase name="calculateDiscountedPrice Discount Percentage Range Validation Tests should throw error when discount percentage is positive infinity" time="0.0000" classname="should throw error when discount percentage is positive infinity">
    </testcase>
  </testsuite>
  <testsuite name="Edge Cases and Special Number Values" timestamp="2025-06-17T13:49:20" tests="7" file="/home/<USER>/learn-to-code/task3.1.1-javascript-unit-tests-creation-copilot/test/index.test.js" time="0.0000" failures="0">
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should handle negative original prices" time="0.0000" classname="should handle negative original prices">
    </testcase>
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should throw error when originalPrice is NaN" time="0.0000" classname="should throw error when originalPrice is NaN">
    </testcase>
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should throw error when discountPercentage is NaN" time="0.0000" classname="should throw error when discountPercentage is NaN">
    </testcase>
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should handle positive infinity as original price" time="0.0000" classname="should handle positive infinity as original price">
    </testcase>
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should handle negative infinity as original price" time="0.0000" classname="should handle negative infinity as original price">
    </testcase>
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should handle very large numbers" time="0.0000" classname="should handle very large numbers">
    </testcase>
    <testcase name="calculateDiscountedPrice Edge Cases and Special Number Values should handle very small positive numbers" time="0.0000" classname="should handle very small positive numbers">
    </testcase>
  </testsuite>
  <testsuite name="Precision and Rounding Tests" timestamp="2025-06-17T13:49:20" tests="3" file="/home/<USER>/learn-to-code/task3.1.1-javascript-unit-tests-creation-copilot/test/index.test.js" time="0.0010" failures="0">
    <testcase name="calculateDiscountedPrice Precision and Rounding Tests should maintain precision for typical currency calculations" time="0.0010" classname="should maintain precision for typical currency calculations">
    </testcase>
    <testcase name="calculateDiscountedPrice Precision and Rounding Tests should handle calculations that result in repeating decimals" time="0.0000" classname="should handle calculations that result in repeating decimals">
    </testcase>
    <testcase name="calculateDiscountedPrice Precision and Rounding Tests should handle multiple decimal places in both parameters" time="0.0000" classname="should handle multiple decimal places in both parameters">
    </testcase>
  </testsuite>
</testsuites>