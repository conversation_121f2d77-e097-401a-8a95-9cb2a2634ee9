{"name": "task3.1.1-javascript-unit-tests-creation-template", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"start": "node src/index.js", "test:junit": "cross-env MOCHA_FILE=junit.xml mocha test --reporter mocha-junit-reporter", "test:xunit": "cross-env MOCHA_FILE=xunit.xml mocha test --reporter mocha-xunit-reporter", "test": "npm run test:junit", "coverage": "nyc --reporter=cobertura --report-dir=./ mocha", "build": "echo \"Dummy build script\""}, "author": "", "license": "ISC", "devDependencies": {"chai": "^4.2.0", "cross-env": "^7.0.2", "deep-eql": "^4.0.0", "mocha": "^10.2.0", "mocha-junit-reporter": "^1.23.3", "mocha-sinon": "^2.1.2", "mocha-xunit-reporter": "^2.1.0", "npx": "^10.2.2", "nyc": "^15.1.0", "sinon": "^9.0.2"}}