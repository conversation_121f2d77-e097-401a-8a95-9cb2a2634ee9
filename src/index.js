/**
 * Calculate the total cost after applying a discount.
 *
 * @param {number} originalPrice - The original price of the item.
 * @param {number} discountPercentage - The discount percentage (0 to 100).
 * @returns {number} - The total cost after discount.
 */

function calculateDiscountedPrice(originalPrice, discountPercentage) {
    if (
        typeof originalPrice !== "number" ||
        typeof discountPercentage !== "number" ||
        isNaN(originalPrice) ||
        isNaN(discountPercentage)
    ) {
        throw new Error("Invalid input type");
    }

    if (discountPercentage < 0 || discountPercentage > 100) {
        throw new Error("Discount percentage should be between 0 and 100");
    }

    const discount = originalPrice * (discountPercentage / 100);
    return originalPrice - discount;
}

module.exports = calculateDiscountedPrice;
