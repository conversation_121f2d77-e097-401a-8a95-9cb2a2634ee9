const calculateDiscountedPrice = require("../src");
const { expect } = require("chai");

describe("calculateDiscountedPrice", function () {
    describe("Valid Input Tests", function () {
        it("should calculate correct discounted price for normal values", function () {
            const result = calculateDiscountedPrice(100, 20);
            expect(result).to.equal(80);
        });

        it("should handle zero discount percentage", function () {
            const result = calculateDiscountedPrice(100, 0);
            expect(result).to.equal(100);
        });

        it("should handle 100% discount", function () {
            const result = calculateDiscountedPrice(100, 100);
            expect(result).to.equal(0);
        });

        it("should handle decimal original prices", function () {
            const result = calculateDiscountedPrice(99.99, 10);
            expect(result).to.be.closeTo(89.991, 0.001);
        });

        it("should handle decimal discount percentages", function () {
            const result = calculateDiscountedPrice(100, 15.5);
            expect(result).to.equal(84.5);
        });

        it("should handle very small original prices", function () {
            const result = calculateDiscountedPrice(0.01, 50);
            expect(result).to.be.closeTo(0.005, 0.001);
        });

        it("should handle very large original prices", function () {
            const result = calculateDiscountedPrice(1000000, 25);
            expect(result).to.equal(750000);
        });

        it("should handle fractional discount percentages", function () {
            const result = calculateDiscountedPrice(200, 33.33);
            expect(result).to.be.closeTo(133.34, 0.01);
        });

        it("should return zero when original price is zero", function () {
            const result = calculateDiscountedPrice(0, 50);
            expect(result).to.equal(0);
        });

        it("should handle very small discount percentages", function () {
            const result = calculateDiscountedPrice(100, 0.01);
            expect(result).to.be.closeTo(99.99, 0.01);
        });

        it("should handle discount percentage close to 100", function () {
            const result = calculateDiscountedPrice(100, 99.99);
            expect(result).to.be.closeTo(0.01, 0.001);
        });
    });

    describe("Input Type Validation Tests", function () {
        it("should throw error when originalPrice is not a number", function () {
            expect(() => calculateDiscountedPrice("100", 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is not a number", function () {
            expect(() => calculateDiscountedPrice(100, "20")).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when originalPrice is null", function () {
            expect(() => calculateDiscountedPrice(null, 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is null", function () {
            expect(() => calculateDiscountedPrice(100, null)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when originalPrice is undefined", function () {
            expect(() => calculateDiscountedPrice(undefined, 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is undefined", function () {
            expect(() => calculateDiscountedPrice(100, undefined)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when originalPrice is boolean", function () {
            expect(() => calculateDiscountedPrice(true, 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is boolean", function () {
            expect(() => calculateDiscountedPrice(100, false)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when originalPrice is an array", function () {
            expect(() => calculateDiscountedPrice([100], 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is an array", function () {
            expect(() => calculateDiscountedPrice(100, [20])).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when originalPrice is an object", function () {
            expect(() => calculateDiscountedPrice({ value: 100 }, 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is an object", function () {
            expect(() => calculateDiscountedPrice(100, { value: 20 })).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when both parameters are invalid types", function () {
            expect(() => calculateDiscountedPrice("100", "20")).to.throw(
                "Invalid input type"
            );
        });
    });

    describe("Discount Percentage Range Validation Tests", function () {
        it("should throw error when discount percentage is negative", function () {
            expect(() => calculateDiscountedPrice(100, -1)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw error when discount percentage is greater than 100", function () {
            expect(() => calculateDiscountedPrice(100, 101)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw error when discount percentage is very negative", function () {
            expect(() => calculateDiscountedPrice(100, -50)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw error when discount percentage is much greater than 100", function () {
            expect(() => calculateDiscountedPrice(100, 200)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw error when discount percentage is negative infinity", function () {
            expect(() => calculateDiscountedPrice(100, -Infinity)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });

        it("should throw error when discount percentage is positive infinity", function () {
            expect(() => calculateDiscountedPrice(100, Infinity)).to.throw(
                "Discount percentage should be between 0 and 100"
            );
        });
    });

    describe("Edge Cases and Special Number Values", function () {
        it("should handle negative original prices", function () {
            // Note: The function doesn't validate negative prices, so this should work
            const result = calculateDiscountedPrice(-100, 20);
            expect(result).to.equal(-80);
        });

        it("should throw error when originalPrice is NaN", function () {
            expect(() => calculateDiscountedPrice(NaN, 20)).to.throw(
                "Invalid input type"
            );
        });

        it("should throw error when discountPercentage is NaN", function () {
            expect(() => calculateDiscountedPrice(100, NaN)).to.throw(
                "Invalid input type"
            );
        });

        it("should handle positive infinity as original price", function () {
            const result = calculateDiscountedPrice(Infinity, 50);
            expect(result).to.be.NaN;
        });

        it("should handle negative infinity as original price", function () {
            const result = calculateDiscountedPrice(-Infinity, 50);
            expect(result).to.be.NaN;
        });

        it("should handle very large numbers", function () {
            const result = calculateDiscountedPrice(
                Number.MAX_SAFE_INTEGER,
                10
            );
            expect(result).to.equal(Number.MAX_SAFE_INTEGER * 0.9);
        });

        it("should handle very small positive numbers", function () {
            const result = calculateDiscountedPrice(Number.MIN_VALUE, 50);
            // Due to floating-point precision, the discount becomes 0, so result equals original
            expect(result).to.equal(Number.MIN_VALUE);
        });
    });

    describe("Precision and Rounding Tests", function () {
        it("should maintain precision for typical currency calculations", function () {
            const result = calculateDiscountedPrice(19.99, 15);
            expect(result).to.be.closeTo(16.9915, 0.0001);
        });

        it("should handle calculations that result in repeating decimals", function () {
            const result = calculateDiscountedPrice(100, 33.33333);
            expect(result).to.be.closeTo(66.66667, 0.00001);
        });

        it("should handle multiple decimal places in both parameters", function () {
            const result = calculateDiscountedPrice(123.456, 12.345);
            expect(result).to.be.closeTo(108.2153568, 0.0000001);
        });
    });
});
